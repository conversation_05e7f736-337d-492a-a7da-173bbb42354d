{"status": true, "message": "Success", "timestamp": 1749376171569, "data": {"aggregation": {"totalCount": 187, "filteredTotalCount": 187, "stops": [{"numberOfStops": 0, "count": 38, "minPrice": {"currencyCode": "IDR", "units": 1215013, "nanos": *********}, "minPriceRound": {"currencyCode": "IDR", "units": 1223354, "nanos": *********}}, {"numberOfStops": 1, "count": 187, "minPrice": {"currencyCode": "IDR", "units": 1215013, "nanos": *********}, "minPriceRound": {"currencyCode": "IDR", "units": 1223354, "nanos": *********}}], "airlines": [{"name": "Garuda Indonesia", "logoUrl": "https://r-xx.bstatic.com/data/airlines_logo/GA.png", "iataCode": "GA", "count": 54, "minPrice": {"currencyCode": "IDR", "units": 1870620, "nanos": *********}}, {"name": "Singapore Airlines", "logoUrl": "https://r-xx.bstatic.com/data/airlines_logo/SQ.png", "iataCode": "SQ", "count": 34, "minPrice": {"currencyCode": "IDR", "units": 9286376, "nanos": 270000000}}, {"name": "Super Air Jet", "logoUrl": "https://r-xx.bstatic.com/data/airlines_logo/IU.png", "iataCode": "IU", "count": 27, "minPrice": {"currencyCode": "IDR", "units": 1215013, "nanos": *********}}, {"name": "Lion Airlines", "logoUrl": "https://r-xx.bstatic.com/data/airlines_logo/JT.png", "iataCode": "JT", "count": 26, "minPrice": {"currencyCode": "IDR", "units": 1278220, "nanos": *********}}, {"name": "Citilink", "logoUrl": "https://r-xx.bstatic.com/data/airlines_logo/QG.png", "iataCode": "QG", "count": 21, "minPrice": {"currencyCode": "IDR", "units": 1711214, "nanos": 90000000}}, {"name": "Pelita Air", "logoUrl": "https://r-xx.bstatic.com/data/airlines_logo/IP.png", "iataCode": "IP", "count": 18, "minPrice": {"currencyCode": "IDR", "units": 1452826, "nanos": 690000000}}, {"name": "Batik Air Indonesia", "logoUrl": "https://r-xx.bstatic.com/data/airlines_logo/ID.png", "iataCode": "ID", "count": 10, "minPrice": {"currencyCode": "IDR", "units": 2824837, "nanos": *********}}, {"name": "Batik Air Malaysia", "logoUrl": "https://r-xx.bstatic.com/data/airlines_logo/OD.png", "iataCode": "OD", "count": 9, "minPrice": {"currencyCode": "IDR", "units": 3952548, "nanos": 660000000}}, {"name": "Malaysia Airlines", "logoUrl": "https://r-xx.bstatic.com/data/airlines_logo/MH.png", "iataCode": "MH", "count": 8, "minPrice": {"currencyCode": "IDR", "units": 7942910, "nanos": 100000000}}, {"name": "PT Indonesia Airasia", "logoUrl": "https://r-xx.bstatic.com/data/airlines_logo/QZ.png", "iataCode": "QZ", "count": 5, "minPrice": {"currencyCode": "IDR", "units": 1314921, "nanos": *********}}, {"name": "<PERSON>oot", "logoUrl": "https://r-xx.bstatic.com/data/airlines_logo/TR.png", "iataCode": "TR", "count": 5, "minPrice": {"currencyCode": "IDR", "units": 8282669, "nanos": 140000000}}, {"name": "TransNusa Aviation", "logoUrl": "https://r-xx.bstatic.com/data/airlines_logo/8B.png", "iataCode": "8B", "count": 3, "minPrice": {"currencyCode": "IDR", "units": 1296200, "nanos": *********}}, {"name": "Wings Air", "logoUrl": "https://r-xx.bstatic.com/data/airlines_logo/IW.png", "iataCode": "IW", "count": 3, "minPrice": {"currencyCode": "IDR", "units": 4999443, "nanos": *********}}], "departureIntervals": [{"start": "01:40", "end": "22:50"}], "flightTimes": [{"arrival": [{"start": "00:00", "end": "05:59", "count": 0}, {"start": "06:00", "end": "11:59", "count": 55}, {"start": "12:00", "end": "17:59", "count": 48}, {"start": "18:00", "end": "23:59", "count": 84}], "departure": [{"start": "00:00", "end": "05:59", "count": 1}, {"start": "06:00", "end": "11:59", "count": 60}, {"start": "12:00", "end": "17:59", "count": 83}, {"start": "18:00", "end": "23:59", "count": 43}]}], "shortLayoverConnection": {"count": 46}, "durationMin": 2, "durationMax": 26, "minPrice": {"currencyCode": "IDR", "units": 1215013, "nanos": *********}, "minRoundPrice": {"currencyCode": "IDR", "units": 1223354, "nanos": *********}, "minPriceFiltered": {"currencyCode": "IDR", "units": 1215013, "nanos": *********}, "baggage": [{"paramName": "includedBaggage", "count": 166, "enabled": true, "baggageType": "CABIN"}, {"paramName": "includedBaggage", "count": 138, "enabled": true, "baggageType": "CHECKIN"}], "budget": {"paramName": "max<PERSON><PERSON><PERSON>", "min": {"currencyCode": "IDR", "units": 1215013, "nanos": *********}, "max": {"currencyCode": "IDR", "units": 20057460, "nanos": *********}}, "budgetPerAdult": {"paramName": "maxBudgetPerAdult", "min": {"currencyCode": "IDR", "units": 1074884, "nanos": *********}, "max": {"currencyCode": "IDR", "units": 17561816, "nanos": 500000000}}, "duration": [{"min": 2, "max": 26, "durationType": "JOURNEY", "enabled": true, "paramName": "maxDuration"}, {"min": 1, "max": 23, "durationType": "LAYOVER", "enabled": true, "paramName": "maxLayoverDuration"}], "filtersOrder": ["stops", "airlines", "flightTimes", "duration"]}, "flightOffers": [{"token": "d6a1f_H4sIAAAAAAAA_y2QXXOiMBiFf029WkKCiKYzmR0XsEvlQz601psMQkC6rOmQrCi_flPoJDnnOefmfScXKT_Fs65XbVNfpNAaBmoueZ1LBgr-V686JWfO_zTXWs-bTnd2qf2yRfh1H-roB9Q1dYrn958s_2xAByriHjM3Cdc-zRJvR3eJZ3vhy4zdpSa6gsyaMwM50TDGE4qCoPQbO2KBRZCc8GZzHKuCS2ICy_Gj1FymWTSVHfmNktBbj6kkgSP6CJ7caNhE6r1Fw8nNXJyFTmnv23gZZKWzf_T3EIaZ77jzGAXi0LZbxXC_eRdBlsSHDyGDtDeitB8Su-99G959p4zHEaxQOwJgLjEaM88FMScqJEFwwlKS9Pi6XDtTlMTCeD7ynaC5ZeCZYC0rZMOvW_Ygq18LBLGmPvLf1VhouyfDPi8et7JV8DRfq1tTqPQyak4PWNmZmrGyYrKSViu2UsAoWpmGgop-aUMRgAAp-qDem7KBWjc43Ph_0mi6F-0BAAA.", "segments": [{"departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "departureTime": "2025-06-19T18:45:00", "arrivalTime": "2025-06-19T19:30:00", "legs": [{"departureTime": "2025-06-19T18:45:00", "arrivalTime": "2025-06-19T19:30:00", "departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "cabinClass": "ECONOMY", "flightInfo": {"facilities": [], "flightNumber": 5109, "planeType": "", "carrierInfo": {"operatingCarrier": "8B", "marketingCarrier": "8B", "operatingCarrierDisclosureText": ""}}, "carriers": ["8B", "8B"], "carriersData": [{"name": "TransNusa Aviation", "code": "8B", "logo": "https://r-xx.bstatic.com/data/airlines_logo/8B.png"}, {"name": "TransNusa Aviation", "code": "8B", "logo": "https://r-xx.bstatic.com/data/airlines_logo/8B.png"}], "totalTime": 6300, "flightStops": [], "amenities": []}], "totalTime": 6300, "travellerCheckedLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 2, "maxWeightPerPiece": 10, "massUnit": "KG"}}], "travellerCabinLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 56, "maxWidth": 36, "maxHeight": 23, "sizeUnit": "CM"}}}], "isAtolProtected": false, "showWarningDestinationAirport": false, "showWarningOriginAirport": false}], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 1296200, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 981649, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 314550, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1297497}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1296200, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1297497}, "carrierTaxBreakdown": [{"carrier": {"name": "TransNusa Aviation", "code": "8B", "logo": "https://r-xx.bstatic.com/data/airlines_logo/8B.png"}, "avgPerAdult": {"currencyCode": "IDR", "units": 305653, "nanos": *********}, "avgPerInfant": {"currencyCode": "IDR", "units": 8897, "nanos": *********}}], "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerPrices": [{"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 1165894, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 845227, "nanos": 60000000}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 305653, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1167747}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1165894, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1167747}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "1", "travellerType": "ADULT"}, {"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 145319, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 136422, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 8897, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 145319, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "2", "travellerType": "KID"}], "priceDisplayRequirements": [], "pointOfSale": "ie", "tripType": "ONEWAY", "posMismatch": {"detectedPointOfSale": "ie", "isPOSMismatch": false, "offerSalesCountry": "ie"}, "includedProductsBySegment": [[{"travellerReference": "1", "travellerProducts": [{"type": "checkedInBaggage", "product": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 2, "maxWeightPerPiece": 10, "massUnit": "KG"}}, {"type": "cabinBaggage", "product": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 56, "maxWidth": 36, "maxHeight": 23, "sizeUnit": "CM"}}}]}]], "includedProducts": {"areAllSegmentsIdentical": true, "segments": [[{"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 56, "maxWidth": 36, "maxHeight": 23, "sizeUnit": "CM"}, "piecePerPax": 1}, {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 2, "maxWeightPerPiece": 10, "massUnit": "KG", "piecePerPax": 2}]]}, "extraProducts": [{"type": "flexibleTicket", "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "carrierPriceBreakdown": []}}], "offerExtras": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "ancillaries": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "preSelected": false, "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "brandedFareInfo": {"fareName": "ECONOMY SEAT", "cabinClass": "ECONOMY", "features": [{"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "2 checked bags (2 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}], "fareAttributes": [], "nonIncludedFeaturesRequired": false, "nonIncludedFeatures": [], "featuresList": [{"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "2 checked bags (2 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}]}, "fareRules": [{"segmentIdentifiers": [{"segmentIndex": 0, "legIndex": 0}], "availablePolicies": [], "unavailablePolicies": [{"type": "CANCEL_BEFORE"}]}], "appliedDiscounts": [], "offerKeyToHighlight": "1_8B5109.DPS20250619", "extraProductDisplayRequirements": {}, "unifiedPriceBreakdown": {"price": {"currencyCode": "IDR", "units": 1296200, "nanos": *********}, "items": [{"scope": "FLIGHT_ADULT", "id": "flight_adult", "title": "Adult (1)", "price": {"currencyCode": "IDR", "units": 1165894, "nanos": *********}, "items": [{"id": "flight_adult-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 845227, "nanos": 60000000}, "items": []}, {"id": "flight_adult-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 305653, "nanos": *********}, "items": []}]}, {"scope": "FLIGHT_INFANT", "id": "flight_infant", "title": "Infant (1)", "price": {"currencyCode": "IDR", "units": 145319, "nanos": *********}, "items": [{"id": "flight_infant-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 136422, "nanos": *********}, "items": []}, {"id": "flight_infant-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 8897, "nanos": *********}, "items": []}]}], "addedItems": []}}, {"token": "d6a1f_H4sIAAAAAAAA_y2QW2-jMBCFf03zVAzmWipZq4hLl4ZLuLVpXixiDKFLQ4RdkfDr68LK9jnfGY00I585v7JnWW76rj1zJnUUtAMf2opTQIYvuRmFnIbhX3dp5aobZXefOy87aL-WsQwfFVkShzx__KHVtQMjaJB3KLws3oa4yII93meBE8QvG3rjEhsJ2nQnCiok2ba9IiMI5v9xRCYwouxo-_5hKZGBIx2YbpjkupUX4Voc0d8nzfPLJdUoctmUKGxOZj8R7z2Zj17h2UXs1k7Zp1ZU1G55n26xEheh62kpjNhb3-8EK6X_waIiS98-GY_ySU3yaS7u0xQ6yi1063QZQYnYEQDdsuGSh4ohfSXCEVRWrDnKD6_W1l0jR6ZhGAvfENRM1d4w2lPCu-Gyo3cUlJZuSOIfvy-qIYUPqlN918PVEPCgbcVtsSL0vGiFj6WwE9ZTYWS1GlPLoAIohk-n37YGq0I7DIECoKBPHLwLm7F1JY2p_QAo_32B7AEAAA..", "segments": [{"departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "departureTime": "2025-06-19T21:30:00", "arrivalTime": "2025-06-19T22:25:00", "legs": [{"departureTime": "2025-06-19T21:30:00", "arrivalTime": "2025-06-19T22:25:00", "departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "cabinClass": "ECONOMY", "flightInfo": {"facilities": [], "flightNumber": 745, "planeType": "", "carrierInfo": {"operatingCarrier": "IU", "marketingCarrier": "IU", "operatingCarrierDisclosureText": ""}}, "carriers": ["IU", "IU"], "carriersData": [{"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}, {"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}], "totalTime": 6900, "flightStops": [], "amenities": []}], "totalTime": 6900, "travellerCheckedLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}], "travellerCabinLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}}, "personalItem": true}], "isAtolProtected": false, "showWarningDestinationAirport": false, "showWarningOriginAirport": false}], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 1215013, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 917886, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 302131, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1223354}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1215013, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1223354}, "carrierTaxBreakdown": [{"carrier": {"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}, "avgPerAdult": {"currencyCode": "IDR", "units": 288415, "nanos": *********}, "avgPerInfant": {"currencyCode": "IDR", "units": 8711, "nanos": *********}}], "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerPrices": [{"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 1074884, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 786468, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 288415, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1075069}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1074884, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1075069}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "1", "travellerType": "ADULT"}, {"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 140129, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 146431, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 8711, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 140129, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "2", "travellerType": "KID"}], "priceDisplayRequirements": [], "pointOfSale": "ie", "tripType": "ONEWAY", "posMismatch": {"detectedPointOfSale": "ie", "isPOSMismatch": false, "offerSalesCountry": "ie"}, "includedProductsBySegment": [[{"travellerReference": "1", "travellerProducts": [{"type": "checkedInBaggage", "product": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}, {"type": "cabinBaggage", "product": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}}}, {"type": "personalItem"}]}]], "includedProducts": {"areAllSegmentsIdentical": true, "segments": [[{"luggageType": "PERSONAL_ITEM", "maxPiece": 1, "piecePerPax": 1}, {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}, "piecePerPax": 1}, {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG", "piecePerPax": 1}]]}, "extraProducts": [{"type": "flexibleTicket", "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "carrierPriceBreakdown": []}}], "offerExtras": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "ancillaries": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "preSelected": false, "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "brandedFareInfo": {"fareName": "Economy", "cabinClass": "ECONOMY", "features": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}], "fareAttributes": [], "nonIncludedFeaturesRequired": false, "nonIncludedFeatures": [], "featuresList": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}]}, "appliedDiscounts": [], "offerKeyToHighlight": "1_IU745.DPS20250619", "extraProductDisplayRequirements": {}, "unifiedPriceBreakdown": {"price": {"currencyCode": "IDR", "units": 1215013, "nanos": *********}, "items": [{"scope": "FLIGHT_ADULT", "id": "flight_adult", "title": "Adult (1)", "price": {"currencyCode": "IDR", "units": 1074884, "nanos": *********}, "items": [{"id": "flight_adult-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 786468, "nanos": *********}, "items": []}, {"id": "flight_adult-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 288415, "nanos": *********}, "items": []}]}, {"scope": "FLIGHT_INFANT", "id": "flight_infant", "title": "Infant (1)", "price": {"currencyCode": "IDR", "units": 140129, "nanos": *********}, "items": [{"id": "flight_infant-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 146431, "nanos": *********}, "items": []}, {"id": "flight_infant-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 8711, "nanos": *********}, "items": []}]}], "addedItems": []}}, {"token": "d6a1f_H4sIAAAAAAAA_y2QW2-jMBSEf03ztBgbCIkrWassl5aGS7ikTfNiETCElg0Vdgvl168LKx_PfDMvts5ViA9-r6pV29RXwZWGgboTXZ0LBorur1r1Ui5d997cajVvetU-pNbDHuGnY6iiX1BV5CnuX3-z_KMBPaiIc8qcJNz5NEu8Az0knuWFDys2CoX3BVk1FwZyomCMF-QFQel_7IkJ1kFyxq57mquiE8QApu1HqbFJs2Ape_KIktDbzakkgc2HCJ6daHIjeV-i6exkDs5Cu7SObbwJstI-fg9jCMPMtx09RgF_btu9ZHh0X3mQJfHzGxdBOmhROkyJNQy-BUffLuP5CVbIPwJgbDCac5dzYixUCILggqUg6elps7OXKIiJsT7zSJBuanjFWcsK0XS3Pfsm2z9rBNeKXOTnTVsrhzvNuuij-Cwl3Ok7OTWFUq-z5tT9qS7UiKUVi5W02rKtBEbR1tAkVPRHG4oABEjSG_VepE3U_ILTV_cPlPxBUu0BAAA.", "segments": [{"departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "departureTime": "2025-06-19T09:10:00", "arrivalTime": "2025-06-19T10:00:00", "legs": [{"departureTime": "2025-06-19T09:10:00", "arrivalTime": "2025-06-19T10:00:00", "departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "cabinClass": "ECONOMY", "flightInfo": {"facilities": [], "flightNumber": 5105, "planeType": "", "carrierInfo": {"operatingCarrier": "8B", "marketingCarrier": "8B", "operatingCarrierDisclosureText": ""}}, "carriers": ["8B", "8B"], "carriersData": [{"name": "TransNusa Aviation", "code": "8B", "logo": "https://r-xx.bstatic.com/data/airlines_logo/8B.png"}, {"name": "TransNusa Aviation", "code": "8B", "logo": "https://r-xx.bstatic.com/data/airlines_logo/8B.png"}], "totalTime": 6600, "flightStops": [], "amenities": []}], "totalTime": 6600, "travellerCheckedLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 2, "maxWeightPerPiece": 10, "massUnit": "KG"}}], "travellerCabinLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 56, "maxWidth": 36, "maxHeight": 23, "sizeUnit": "CM"}}}], "isAtolProtected": false, "showWarningDestinationAirport": false, "showWarningOriginAirport": false}], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 1296200, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 981649, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 314550, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1297497}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1296200, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1297497}, "carrierTaxBreakdown": [{"carrier": {"name": "TransNusa Aviation", "code": "8B", "logo": "https://r-xx.bstatic.com/data/airlines_logo/8B.png"}, "avgPerAdult": {"currencyCode": "IDR", "units": 305653, "nanos": *********}, "avgPerInfant": {"currencyCode": "IDR", "units": 8897, "nanos": *********}}], "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerPrices": [{"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 1165894, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 845227, "nanos": 60000000}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 305653, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1167747}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1165894, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1167747}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "1", "travellerType": "ADULT"}, {"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 145319, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 136422, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 8897, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 145319, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "2", "travellerType": "KID"}], "priceDisplayRequirements": [], "pointOfSale": "ie", "tripType": "ONEWAY", "posMismatch": {"detectedPointOfSale": "ie", "isPOSMismatch": false, "offerSalesCountry": "ie"}, "includedProductsBySegment": [[{"travellerReference": "1", "travellerProducts": [{"type": "checkedInBaggage", "product": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 2, "maxWeightPerPiece": 10, "massUnit": "KG"}}, {"type": "cabinBaggage", "product": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 56, "maxWidth": 36, "maxHeight": 23, "sizeUnit": "CM"}}}]}]], "includedProducts": {"areAllSegmentsIdentical": true, "segments": [[{"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 56, "maxWidth": 36, "maxHeight": 23, "sizeUnit": "CM"}, "piecePerPax": 1}, {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 2, "maxWeightPerPiece": 10, "massUnit": "KG", "piecePerPax": 2}]]}, "extraProducts": [{"type": "flexibleTicket", "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "carrierPriceBreakdown": []}}], "offerExtras": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "ancillaries": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "preSelected": false, "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "brandedFareInfo": {"fareName": "ECONOMY SEAT", "cabinClass": "ECONOMY", "features": [{"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "2 checked bags (2 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}], "fareAttributes": [], "nonIncludedFeaturesRequired": false, "nonIncludedFeatures": [], "featuresList": [{"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "2 checked bags (2 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}]}, "fareRules": [{"segmentIdentifiers": [{"segmentIndex": 0, "legIndex": 0}], "availablePolicies": [], "unavailablePolicies": [{"type": "CANCEL_BEFORE"}]}], "appliedDiscounts": [], "offerKeyToHighlight": "1_8B5105.DPS20250619", "extraProductDisplayRequirements": {}, "unifiedPriceBreakdown": {"price": {"currencyCode": "IDR", "units": 1296200, "nanos": *********}, "items": [{"scope": "FLIGHT_ADULT", "id": "flight_adult", "title": "Adult (1)", "price": {"currencyCode": "IDR", "units": 1165894, "nanos": *********}, "items": [{"id": "flight_adult-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 845227, "nanos": 60000000}, "items": []}, {"id": "flight_adult-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 305653, "nanos": *********}, "items": []}]}, {"scope": "FLIGHT_INFANT", "id": "flight_infant", "title": "Infant (1)", "price": {"currencyCode": "IDR", "units": 145319, "nanos": *********}, "items": [{"id": "flight_infant-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 136422, "nanos": *********}, "items": []}, {"id": "flight_infant-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 8897, "nanos": *********}, "items": []}]}], "addedItems": []}}, {"token": "d6a1f_H4sIAAAAAAAA_y2Q226jMBiEn6a5WgwmFHAlaxUBydJwCKcmzY0FxhC6bKiwV6Q8_bqwsv-Zb0aWbPkmxCd_UdWm79qb4ErHQDuIoS0FA3T4ozajlGoYfnf3Vi27UXVPmXM4QvRaRCr8oamKXPTl_ScrPzswggZ7l9xLo11A8tQ_kVPqO3502LCHUPhI8aarGCixghBakVMMs_84YhM8h-kV7feXpaKDwAYw3SDODCvLD2s54l-2H9jnJdU4dPkUz_tCTiznHM9XL_dQHrm1U_SJFea1W3xNj0iL8sD1tgkM-VvfHyVrxf6dh3mavH1wEWaTHmfTnDnTFDjaI3DrZLmCUflGAAwLwSUPJcfGSlRgqK1YC5xdXq2du0aBLQ2tpx4Ybk0dbTjrGRXdcD-yL5xcbQgV-Y9_7_qzcnrSnUoz52qQ8LTdyd0STept0ZJAXZdeESORRlerSUOr75oRaJulhIZ8x45AoAEo6YP4Z2kzQds7tK1_xjwEbu0BAAA.", "segments": [{"departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "departureTime": "2025-06-19T22:50:00", "arrivalTime": "2025-06-19T23:40:00", "legs": [{"departureTime": "2025-06-19T22:50:00", "arrivalTime": "2025-06-19T23:40:00", "departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "cabinClass": "ECONOMY", "flightInfo": {"facilities": [], "flightNumber": 811, "planeType": "", "carrierInfo": {"operatingCarrier": "QZ", "marketingCarrier": "QZ", "operatingCarrierDisclosureText": ""}}, "carriers": ["QZ", "QZ"], "carriersData": [{"name": "PT Indonesia Airasia", "code": "QZ", "logo": "https://r-xx.bstatic.com/data/airlines_logo/QZ.png"}, {"name": "PT Indonesia Airasia", "code": "QZ", "logo": "https://r-xx.bstatic.com/data/airlines_logo/QZ.png"}], "totalTime": 6600, "flightStops": [], "amenities": []}], "totalTime": 6600, "travellerCheckedLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}], "travellerCabinLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 56, "maxWidth": 36, "maxHeight": 23, "sizeUnit": "CM"}}, "personalItem": true}], "isAtolProtected": false, "showWarningDestinationAirport": false, "showWarningOriginAirport": false}], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 1314921, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 1022984, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 291936, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1316033}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1314921, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1316033}, "carrierTaxBreakdown": [{"carrier": {"name": "PT Indonesia Airasia", "code": "QZ", "logo": "https://r-xx.bstatic.com/data/airlines_logo/QZ.png"}, "avgPerAdult": {"currencyCode": "IDR", "units": 291936, "nanos": *********}}], "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerPrices": [{"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 1161075, "nanos": 70000000}, "baseFare": {"currencyCode": "IDR", "units": 869138, "nanos": 90000000}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 291936, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1167747}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1161075, "nanos": 70000000}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1167747}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "1", "travellerType": "ADULT"}, {"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 153846, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 153846, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 166821}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 153846, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 166821}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "2", "travellerType": "KID"}], "priceDisplayRequirements": [], "pointOfSale": "ie", "tripType": "ONEWAY", "posMismatch": {"detectedPointOfSale": "ie", "isPOSMismatch": false, "offerSalesCountry": "ie"}, "includedProductsBySegment": [[{"travellerReference": "1", "travellerProducts": [{"type": "checkedInBaggage", "product": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}, {"type": "cabinBaggage", "product": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 56, "maxWidth": 36, "maxHeight": 23, "sizeUnit": "CM"}}}, {"type": "personalItem"}]}]], "includedProducts": {"areAllSegmentsIdentical": true, "segments": [[{"luggageType": "PERSONAL_ITEM", "maxPiece": 1, "piecePerPax": 1}, {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 56, "maxWidth": 36, "maxHeight": 23, "sizeUnit": "CM"}, "piecePerPax": 1}, {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG", "piecePerPax": 1}]]}, "extraProducts": [{"type": "flexibleTicket", "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "carrierPriceBreakdown": []}}], "offerExtras": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "ancillaries": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "preSelected": false, "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "brandedFareInfo": {"fareName": "Low fare", "cabinClass": "ECONOMY", "features": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}], "fareAttributes": [], "nonIncludedFeaturesRequired": false, "nonIncludedFeatures": [], "featuresList": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}]}, "fareRules": [{"segmentIdentifiers": [{"segmentIndex": 0, "legIndex": 0}], "availablePolicies": [], "unavailablePolicies": [{"type": "CANCEL_BEFORE"}]}], "appliedDiscounts": [], "offerKeyToHighlight": "1_QZ811.DPS20250619", "extraProductDisplayRequirements": {}, "unifiedPriceBreakdown": {"price": {"currencyCode": "IDR", "units": 1314921, "nanos": *********}, "items": [{"scope": "FLIGHT_ADULT", "id": "flight_adult", "title": "Adult (1)", "price": {"currencyCode": "IDR", "units": 1161075, "nanos": 70000000}, "items": [{"id": "flight_adult-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 869138, "nanos": 90000000}, "items": []}, {"id": "flight_adult-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 291936, "nanos": *********}, "items": []}]}, {"scope": "FLIGHT_INFANT", "id": "flight_infant", "title": "Infant (1)", "price": {"currencyCode": "IDR", "units": 153846, "nanos": *********}, "items": [{"id": "flight_infant-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 153846, "nanos": *********}, "items": []}]}], "addedItems": []}}, {"token": "d6a1f_H4sIAAAAAAAA_y2Q226jMBRFv6Z5GgwGAnUlaxQBaUm4BDDN5cUiYAgtDR3sEUm-flwY2d5r7fNiyxchvvmLqtZd21wEV1oGml70TSEYKPsvtR5knPv-s702atEOqrvLnNctRJs8UuEvTVXkKl-Ov1nx3YIB1Ng7EC-NVgElqb-ju9R3_Oh1wW5C4UOJF-2ZgQIrCKFZeYlh9l8HbIFlmJ7Qen2YRmUvsAksN4gz087Ifh4O-M06plE-tQqHLh_jx8mPH-tYnr10j3iIRG7l5F1ih6Ry8_t4i7SIBK5nJDDk7123la7l6yMPSZq8f3ARZqMeZ6NG2nEMHO0WuFUyXcFK-UYATBvBqfcFx-ZspcBQm7USODts7JU7V4GtZ2RNfsPQsHS04KxjpWj765bd8YboS0V-49-r5PZJd9Cf4f7MpDwZK7kbqsm8TFnQ0w_O1EwkyhkVrY2lLoVRWGhMSk1_aksh0ACU9kH9vcSD2kbDPi__AKTmWnXrAQAA", "segments": [{"departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "departureTime": "2025-06-19T21:00:00", "arrivalTime": "2025-06-19T21:55:00", "legs": [{"departureTime": "2025-06-19T21:00:00", "arrivalTime": "2025-06-19T21:55:00", "departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "cabinClass": "ECONOMY", "flightInfo": {"facilities": [], "flightNumber": 25, "planeType": "", "carrierInfo": {"operatingCarrier": "JT", "marketingCarrier": "JT", "operatingCarrierDisclosureText": ""}}, "carriers": ["JT", "JT"], "carriersData": [{"name": "Lion Airlines", "code": "JT", "logo": "https://r-xx.bstatic.com/data/airlines_logo/JT.png"}, {"name": "Lion Airlines", "code": "JT", "logo": "https://r-xx.bstatic.com/data/airlines_logo/JT.png"}], "totalTime": 6900, "flightStops": [], "amenities": []}], "totalTime": 6900, "travellerCheckedLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}], "travellerCabinLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}}}], "isAtolProtected": false, "showWarningDestinationAirport": false, "showWarningOriginAirport": false}], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 1278220, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 978313, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 299907, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": 0, "units": 1278962}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1278220, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": 0, "units": 1278962}, "carrierTaxBreakdown": [{"carrier": {"name": "Lion Airlines", "code": "JT", "logo": "https://r-xx.bstatic.com/data/airlines_logo/JT.png"}, "avgPerAdult": {"currencyCode": "IDR", "units": 291010, "nanos": *********}, "avgPerInfant": {"currencyCode": "IDR", "units": 8897, "nanos": *********}}], "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerPrices": [{"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 1129564, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 838554, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 291010, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1130676}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1129564, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1130676}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "1", "travellerType": "ADULT"}, {"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 151992, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 139759, "nanos": 40000000}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 8897, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 166821}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 151992, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 166821}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "2", "travellerType": "KID"}], "priceDisplayRequirements": [], "pointOfSale": "ie", "tripType": "ONEWAY", "posMismatch": {"detectedPointOfSale": "ie", "isPOSMismatch": false, "offerSalesCountry": "ie"}, "includedProductsBySegment": [[{"travellerReference": "1", "travellerProducts": [{"type": "checkedInBaggage", "product": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}, {"type": "cabinBaggage", "product": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}}}]}]], "includedProducts": {"areAllSegmentsIdentical": true, "segments": [[{"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}, "piecePerPax": 1}, {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG", "piecePerPax": 1}]]}, "extraProducts": [{"type": "flexibleTicket", "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "carrierPriceBreakdown": []}}], "offerExtras": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "ancillaries": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "preSelected": false, "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "brandedFareInfo": {"fareName": "Economy", "cabinClass": "ECONOMY", "features": [{"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}], "fareAttributes": [], "nonIncludedFeaturesRequired": false, "nonIncludedFeatures": [], "featuresList": [{"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}]}, "appliedDiscounts": [], "offerKeyToHighlight": "1_JT25.DPS20250619", "extraProductDisplayRequirements": {}, "unifiedPriceBreakdown": {"price": {"currencyCode": "IDR", "units": 1278220, "nanos": *********}, "items": [{"scope": "FLIGHT_ADULT", "id": "flight_adult", "title": "Adult (1)", "price": {"currencyCode": "IDR", "units": 1129564, "nanos": *********}, "items": [{"id": "flight_adult-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 838554, "nanos": *********}, "items": []}, {"id": "flight_adult-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 291010, "nanos": *********}, "items": []}]}, {"scope": "FLIGHT_INFANT", "id": "flight_infant", "title": "Infant (1)", "price": {"currencyCode": "IDR", "units": 151992, "nanos": *********}, "items": [{"id": "flight_infant-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 139759, "nanos": 40000000}, "items": []}, {"id": "flight_infant-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 8897, "nanos": *********}, "items": []}]}], "addedItems": []}}, {"token": "d6a1f_H4sIAAAAAAAA_y2Q226jMBRFv6Z5KgYDCaWSNYqAtCRcwq1J-2KBMYQOE0fYFSlfXxdGPt57bR9Zx_JFiBt_VtWm79qL4EpHQcsEa0tBAWH_1GaQUjH2t7u2atkNqnvMnJcDtPdFpMJHTVXkIs_vf2h568AAGuSdcy-NtgHOU_-Ij6nv-NHLit6FwgeCVl1FQYkU27YX5ATB7D8OaAPWYfph73bn-YgwgUywcYM4M60sX66QAb1ap8zYz6lGocvHWONTPO1iuU_x9OHlnp1Hbu0UfWKFee0W3-M90qI8cD0jgSF_6_uDZK3YvfMwT5O3Ty7CbNTjbJyKbBwDR7sHbp3MIyiRbwTAtGw4Z1ZyZC5EBILagrVA2Xlvbd0lCrSxjaV1R9DY6PaK054S0bHrgX4jv7AMqMh__Lrqa-XwoDvlwIaukvBgbGW1WJN6mbXE7qu0CpuJNLJYjZt189ulGD4RU0KDdakdhkADUNIn9k_SJmzdRsOsfgB6D-0Q7AEAAA..", "segments": [{"departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "departureTime": "2025-06-19T08:05:00", "arrivalTime": "2025-06-19T09:00:00", "legs": [{"departureTime": "2025-06-19T08:05:00", "arrivalTime": "2025-06-19T09:00:00", "departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "cabinClass": "ECONOMY", "flightInfo": {"facilities": [], "flightNumber": 731, "planeType": "", "carrierInfo": {"operatingCarrier": "IU", "marketingCarrier": "IU", "operatingCarrierDisclosureText": ""}}, "carriers": ["IU", "IU"], "carriersData": [{"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}, {"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}], "totalTime": 6900, "flightStops": [], "amenities": []}], "totalTime": 6900, "travellerCheckedLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}], "travellerCabinLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}}, "personalItem": true}], "isAtolProtected": false, "showWarningDestinationAirport": false, "showWarningOriginAirport": false}], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 999073, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 300463, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1297497}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1297497}, "carrierTaxBreakdown": [{"carrier": {"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}, "avgPerAdult": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "avgPerInfant": {"currencyCode": "IDR", "units": 9082, "nanos": *********}}], "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerPrices": [{"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 846524, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1149212}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1149212}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "1", "travellerType": "ADULT"}, {"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 137534, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 9082, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "2", "travellerType": "KID"}], "priceDisplayRequirements": [], "pointOfSale": "ie", "tripType": "ONEWAY", "posMismatch": {"detectedPointOfSale": "ie", "isPOSMismatch": false, "offerSalesCountry": "ie"}, "includedProductsBySegment": [[{"travellerReference": "1", "travellerProducts": [{"type": "checkedInBaggage", "product": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}, {"type": "cabinBaggage", "product": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}}}, {"type": "personalItem"}]}]], "includedProducts": {"areAllSegmentsIdentical": true, "segments": [[{"luggageType": "PERSONAL_ITEM", "maxPiece": 1, "piecePerPax": 1}, {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}, "piecePerPax": 1}, {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG", "piecePerPax": 1}]]}, "extraProducts": [{"type": "flexibleTicket", "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "carrierPriceBreakdown": []}}], "offerExtras": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "ancillaries": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "preSelected": false, "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "brandedFareInfo": {"fareName": "Economy", "cabinClass": "ECONOMY", "features": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}], "fareAttributes": [], "nonIncludedFeaturesRequired": false, "nonIncludedFeatures": [], "featuresList": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}]}, "appliedDiscounts": [], "offerKeyToHighlight": "1_IU731.DPS20250619", "extraProductDisplayRequirements": {}, "unifiedPriceBreakdown": {"price": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "items": [{"scope": "FLIGHT_ADULT", "id": "flight_adult", "title": "Adult (1)", "price": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "items": [{"id": "flight_adult-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 846524, "nanos": *********}, "items": []}, {"id": "flight_adult-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "items": []}]}, {"scope": "FLIGHT_INFANT", "id": "flight_infant", "title": "Infant (1)", "price": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "items": [{"id": "flight_infant-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 137534, "nanos": *********}, "items": []}, {"id": "flight_infant-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 9082, "nanos": *********}, "items": []}]}], "addedItems": []}}, {"token": "d6a1f_H4sIAAAAAAAA_y2QW2-jMBCFf03ztBjMJawrWVXEpSXhEm5N2heLGEPosiHCXpHy69cLK9vnfGfmYUa-CnHnz6ra9F17FVzpGGgHMbSVYIAOv9VmlHIZhl_drVWrblTdY-68HiDal7EKf2iqIg99_nhh1b0DI2iwdy68LN6FpMiCIzlmgRPErxv2EAofKd50FwYqrCCEVuQUw_w_jngLrCj7RL5_Xkp0ENgEWzdMctPOC28tjvjNPuXGfkk1jlw-JRqfk9lP5Dsl86dXeKiI3dop-9SOitotv6dHrMVF6HpGCiP-3vcHyVrpf_CoyNL3Ly6ifNKTfJrLfJpCR3uEbp0uIxiVOwJg2ggueag4NleiAkNtxVrg_Ly3d-4aBd4iY209MDS2Otpw1jMquuF2YN84KG0LKvIf_9x0Szk86U7FucGEhCdjJ29LNKnXRSviW9IuxEyl0dVq0ljNvy4j8Cc1JTREl9oRCDQAJX2R4CRtJvZ9MszLX5JnXEjsAQAA", "segments": [{"departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "departureTime": "2025-06-19T09:05:00", "arrivalTime": "2025-06-19T10:00:00", "legs": [{"departureTime": "2025-06-19T09:05:00", "arrivalTime": "2025-06-19T10:00:00", "departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "cabinClass": "ECONOMY", "flightInfo": {"facilities": [], "flightNumber": 751, "planeType": "", "carrierInfo": {"operatingCarrier": "IU", "marketingCarrier": "IU", "operatingCarrierDisclosureText": ""}}, "carriers": ["IU", "IU"], "carriersData": [{"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}, {"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}], "totalTime": 6900, "flightStops": [], "amenities": []}], "totalTime": 6900, "travellerCheckedLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}], "travellerCabinLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}}, "personalItem": true}], "isAtolProtected": false, "showWarningDestinationAirport": false, "showWarningOriginAirport": false}], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 999073, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 300463, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1297497}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1297497}, "carrierTaxBreakdown": [{"carrier": {"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}, "avgPerAdult": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "avgPerInfant": {"currencyCode": "IDR", "units": 9082, "nanos": *********}}], "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerPrices": [{"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 846524, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1149212}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1149212}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "1", "travellerType": "ADULT"}, {"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 137534, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 9082, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "2", "travellerType": "KID"}], "priceDisplayRequirements": [], "pointOfSale": "ie", "tripType": "ONEWAY", "posMismatch": {"detectedPointOfSale": "ie", "isPOSMismatch": false, "offerSalesCountry": "ie"}, "includedProductsBySegment": [[{"travellerReference": "1", "travellerProducts": [{"type": "checkedInBaggage", "product": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}, {"type": "cabinBaggage", "product": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}}}, {"type": "personalItem"}]}]], "includedProducts": {"areAllSegmentsIdentical": true, "segments": [[{"luggageType": "PERSONAL_ITEM", "maxPiece": 1, "piecePerPax": 1}, {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}, "piecePerPax": 1}, {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG", "piecePerPax": 1}]]}, "extraProducts": [{"type": "flexibleTicket", "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "carrierPriceBreakdown": []}}], "offerExtras": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "ancillaries": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "preSelected": false, "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "brandedFareInfo": {"fareName": "Economy", "cabinClass": "ECONOMY", "features": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}], "fareAttributes": [], "nonIncludedFeaturesRequired": false, "nonIncludedFeatures": [], "featuresList": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}]}, "appliedDiscounts": [], "offerKeyToHighlight": "1_IU751.DPS20250619", "extraProductDisplayRequirements": {}, "unifiedPriceBreakdown": {"price": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "items": [{"scope": "FLIGHT_ADULT", "id": "flight_adult", "title": "Adult (1)", "price": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "items": [{"id": "flight_adult-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 846524, "nanos": *********}, "items": []}, {"id": "flight_adult-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "items": []}]}, {"scope": "FLIGHT_INFANT", "id": "flight_infant", "title": "Infant (1)", "price": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "items": [{"id": "flight_infant-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 137534, "nanos": *********}, "items": []}, {"id": "flight_infant-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 9082, "nanos": *********}, "items": []}]}], "addedItems": []}}, {"token": "d6a1f_H4sIAAAAAAAA_y2Q3W6jMBCFn6a5WgwGEpZKVhUBSUn4CRiatDcWMYbQZSHC7pLy9OuFle1zvjNzMSPfhLjzZ1Wt2qa-Ca40DNS96OtCMED732o1SLn2_a-mq9WiGVT3hJ39EdqHPFLhD01V5KHP7y-suDdgABXyLpmXRtuAZKl_IqfUd_xov2IPofCBolVzZaBAim3bC3KKIP6PA9qAdZh-2LvdZS7RXiATbNwgxqaFM2MpDujVOmPjMKcShS4fY41P8bSL5TvH04eXeXYWuaWTt4kVZqWbf4-PSIuywPWMBIb8rW2PkrV8987DLE3ePrkI8ajHeJxyPI6Boz0Ct0zmEYzKHQEwLRvOuS84MheiAkFtwVIgfDlYW3eJAm1sY2k9EDQ2ur3irGVUNH13ZN_Iz621pch__Or0tXJ80p3ij-junYQnYytvTTSpt1kLso-lXYmZSKOLlaRaV_-6jMCf1JRQEV1qQyDQAJT0SfyztIlY99Ewr38B71CeU-wBAAA.", "segments": [{"departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "departureTime": "2025-06-19T10:00:00", "arrivalTime": "2025-06-19T10:55:00", "legs": [{"departureTime": "2025-06-19T10:00:00", "arrivalTime": "2025-06-19T10:55:00", "departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "cabinClass": "ECONOMY", "flightInfo": {"facilities": [], "flightNumber": 757, "planeType": "", "carrierInfo": {"operatingCarrier": "IU", "marketingCarrier": "IU", "operatingCarrierDisclosureText": ""}}, "carriers": ["IU", "IU"], "carriersData": [{"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}, {"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}], "totalTime": 6900, "flightStops": [], "amenities": []}], "totalTime": 6900, "travellerCheckedLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}], "travellerCabinLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}}, "personalItem": true}], "isAtolProtected": false, "showWarningDestinationAirport": false, "showWarningOriginAirport": false}], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 999073, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 300463, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1297497}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1297497}, "carrierTaxBreakdown": [{"carrier": {"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}, "avgPerAdult": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "avgPerInfant": {"currencyCode": "IDR", "units": 9082, "nanos": *********}}], "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerPrices": [{"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 846524, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1149212}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1149212}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "1", "travellerType": "ADULT"}, {"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 137534, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 9082, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "2", "travellerType": "KID"}], "priceDisplayRequirements": [], "pointOfSale": "ie", "tripType": "ONEWAY", "posMismatch": {"detectedPointOfSale": "ie", "isPOSMismatch": false, "offerSalesCountry": "ie"}, "includedProductsBySegment": [[{"travellerReference": "1", "travellerProducts": [{"type": "checkedInBaggage", "product": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}, {"type": "cabinBaggage", "product": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}}}, {"type": "personalItem"}]}]], "includedProducts": {"areAllSegmentsIdentical": true, "segments": [[{"luggageType": "PERSONAL_ITEM", "maxPiece": 1, "piecePerPax": 1}, {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}, "piecePerPax": 1}, {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG", "piecePerPax": 1}]]}, "extraProducts": [{"type": "flexibleTicket", "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "carrierPriceBreakdown": []}}], "offerExtras": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "ancillaries": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "preSelected": false, "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "brandedFareInfo": {"fareName": "Economy", "cabinClass": "ECONOMY", "features": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}], "fareAttributes": [], "nonIncludedFeaturesRequired": false, "nonIncludedFeatures": [], "featuresList": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}]}, "appliedDiscounts": [], "offerKeyToHighlight": "1_IU757.DPS20250619", "extraProductDisplayRequirements": {}, "unifiedPriceBreakdown": {"price": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "items": [{"scope": "FLIGHT_ADULT", "id": "flight_adult", "title": "Adult (1)", "price": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "items": [{"id": "flight_adult-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 846524, "nanos": *********}, "items": []}, {"id": "flight_adult-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "items": []}]}, {"scope": "FLIGHT_INFANT", "id": "flight_infant", "title": "Infant (1)", "price": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "items": [{"id": "flight_infant-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 137534, "nanos": *********}, "items": []}, {"id": "flight_infant-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 9082, "nanos": *********}, "items": []}]}], "addedItems": []}}, {"token": "d6a1f_H4sIAAAAAAAA_y2QX0-DMBTFP417kkKBDTFpzAJM2fgzKLjpS8NKYSiOhdYw-fRWMG3P-Z17H-5Nz0Jc-aOqVm1TnwVXGgbqTnR1IRig3Zda9VJOXffZXGq1aHrV3WPneQftbR6p8F5TFXno49sTK64N6EGFvGPmpdE6IFnq78k-9R0_el6wm1B4T9GiOTFQIMW27Rk5RRD_Y49WYBmm7_Zmc5xKtBPIBCs3iLFp4Syaiz16sQ7Y2E6pRKHLh1jjYzxuYvkO8fjuZZ6dRW7p5G1ihVnp5j_DLdKiLHA9I4Ehf23bnWQt37zxMEuT1w8uQjzoMR7GHA9D4Gi3wC2TaQSjckcATMuGU-4KjsyZqEBQm7EUCB-31tqdo0Ar25hbNwSNlW4vOGsZFU132bEf5OeWCRX5j98Xfans7nSn4A9V_SnhzljLWxNN6nnSgvgv0k7ETKTR2UpSLau_LiPwgZoSKqJLbQgEGoCSPoh_kDYS6zoY5ukXq1QjvuwBAAA.", "segments": [{"departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "departureTime": "2025-06-19T11:05:00", "arrivalTime": "2025-06-19T12:00:00", "legs": [{"departureTime": "2025-06-19T11:05:00", "arrivalTime": "2025-06-19T12:00:00", "departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "cabinClass": "ECONOMY", "flightInfo": {"facilities": [], "flightNumber": 741, "planeType": "", "carrierInfo": {"operatingCarrier": "IU", "marketingCarrier": "IU", "operatingCarrierDisclosureText": ""}}, "carriers": ["IU", "IU"], "carriersData": [{"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}, {"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}], "totalTime": 6900, "flightStops": [], "amenities": []}], "totalTime": 6900, "travellerCheckedLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}], "travellerCabinLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}}, "personalItem": true}], "isAtolProtected": false, "showWarningDestinationAirport": false, "showWarningOriginAirport": false}], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 999073, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 300463, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1297497}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1297497}, "carrierTaxBreakdown": [{"carrier": {"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}, "avgPerAdult": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "avgPerInfant": {"currencyCode": "IDR", "units": 9082, "nanos": *********}}], "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerPrices": [{"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 846524, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1149212}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1149212}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "1", "travellerType": "ADULT"}, {"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 137534, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 9082, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "2", "travellerType": "KID"}], "priceDisplayRequirements": [], "pointOfSale": "ie", "tripType": "ONEWAY", "posMismatch": {"detectedPointOfSale": "ie", "isPOSMismatch": false, "offerSalesCountry": "ie"}, "includedProductsBySegment": [[{"travellerReference": "1", "travellerProducts": [{"type": "checkedInBaggage", "product": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}, {"type": "cabinBaggage", "product": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}}}, {"type": "personalItem"}]}]], "includedProducts": {"areAllSegmentsIdentical": true, "segments": [[{"luggageType": "PERSONAL_ITEM", "maxPiece": 1, "piecePerPax": 1}, {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}, "piecePerPax": 1}, {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG", "piecePerPax": 1}]]}, "extraProducts": [{"type": "flexibleTicket", "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "carrierPriceBreakdown": []}}], "offerExtras": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "ancillaries": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "preSelected": false, "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "brandedFareInfo": {"fareName": "Economy", "cabinClass": "ECONOMY", "features": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}], "fareAttributes": [], "nonIncludedFeaturesRequired": false, "nonIncludedFeatures": [], "featuresList": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}]}, "appliedDiscounts": [], "offerKeyToHighlight": "1_IU741.DPS20250619", "extraProductDisplayRequirements": {}, "unifiedPriceBreakdown": {"price": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "items": [{"scope": "FLIGHT_ADULT", "id": "flight_adult", "title": "Adult (1)", "price": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "items": [{"id": "flight_adult-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 846524, "nanos": *********}, "items": []}, {"id": "flight_adult-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "items": []}]}, {"scope": "FLIGHT_INFANT", "id": "flight_infant", "title": "Infant (1)", "price": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "items": [{"id": "flight_infant-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 137534, "nanos": *********}, "items": []}, {"id": "flight_infant-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 9082, "nanos": *********}, "items": []}]}], "addedItems": []}}, {"token": "d6a1f_H4sIAAAAAAAA_y2Q3Y6bMBCFn2ZzVQwGEuqVrCoCsiXhJ2DYZPfGIsYQUhoi7IosT18XKtvnfGfmYka-SvkQr7ped21zlUJrOWh62Tel5ID1v_V6UHLp-1_tvdHLdtC9I3HfDhDti1iH3wxdU4e9fvzg5aMFA6ixf879LN6GNM-CIz1mgRvEbyv-lJoYGF61Fw5KrCGEFhQMQ_IfB7wB6yj7RLvdeS6xXmIbbLwwIbZDcnspDvincyLWfk4VjjwxJoaYkmmXqHdKpk8_91Eee5VbdKkT5ZVXfI3P2Ijz0POtFEbivesOio1i9yGiPEvfb0JGZDQTMk4FGcfQNZ6hV6XzCM7UjgDYDoJz7kuBl1V6JjE0FqwkJue9s_WWKPEGWUvriaG1MdFK8I4z2fb3A__CQeFYa03945-7udYOL6ZbyoeENwUv1lbdhhpKr7OWNM6UXaidKmOLVbRe1_-6nMLvzFZQU1NpSyEwAFR0o8FJ2USdx2jZl79_GSHS7AEAAA..", "segments": [{"departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "departureTime": "2025-06-19T14:15:00", "arrivalTime": "2025-06-19T15:10:00", "legs": [{"departureTime": "2025-06-19T14:15:00", "arrivalTime": "2025-06-19T15:10:00", "departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "cabinClass": "ECONOMY", "flightInfo": {"facilities": [], "flightNumber": 735, "planeType": "", "carrierInfo": {"operatingCarrier": "IU", "marketingCarrier": "IU", "operatingCarrierDisclosureText": ""}}, "carriers": ["IU", "IU"], "carriersData": [{"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}, {"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}], "totalTime": 6900, "flightStops": [], "amenities": []}], "totalTime": 6900, "travellerCheckedLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}], "travellerCabinLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}}, "personalItem": true}], "isAtolProtected": false, "showWarningDestinationAirport": false, "showWarningOriginAirport": false}], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 999073, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 300463, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1297497}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1297497}, "carrierTaxBreakdown": [{"carrier": {"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}, "avgPerAdult": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "avgPerInfant": {"currencyCode": "IDR", "units": 9082, "nanos": *********}}], "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerPrices": [{"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 846524, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1149212}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1149212}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "1", "travellerType": "ADULT"}, {"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 137534, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 9082, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "2", "travellerType": "KID"}], "priceDisplayRequirements": [], "pointOfSale": "ie", "tripType": "ONEWAY", "posMismatch": {"detectedPointOfSale": "ie", "isPOSMismatch": false, "offerSalesCountry": "ie"}, "includedProductsBySegment": [[{"travellerReference": "1", "travellerProducts": [{"type": "checkedInBaggage", "product": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}, {"type": "cabinBaggage", "product": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}}}, {"type": "personalItem"}]}]], "includedProducts": {"areAllSegmentsIdentical": true, "segments": [[{"luggageType": "PERSONAL_ITEM", "maxPiece": 1, "piecePerPax": 1}, {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}, "piecePerPax": 1}, {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG", "piecePerPax": 1}]]}, "extraProducts": [{"type": "flexibleTicket", "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "carrierPriceBreakdown": []}}], "offerExtras": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "ancillaries": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "preSelected": false, "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "brandedFareInfo": {"fareName": "Economy", "cabinClass": "ECONOMY", "features": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}], "fareAttributes": [], "nonIncludedFeaturesRequired": false, "nonIncludedFeatures": [], "featuresList": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}]}, "appliedDiscounts": [], "offerKeyToHighlight": "1_IU735.DPS20250619", "extraProductDisplayRequirements": {}, "unifiedPriceBreakdown": {"price": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "items": [{"scope": "FLIGHT_ADULT", "id": "flight_adult", "title": "Adult (1)", "price": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "items": [{"id": "flight_adult-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 846524, "nanos": *********}, "items": []}, {"id": "flight_adult-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "items": []}]}, {"scope": "FLIGHT_INFANT", "id": "flight_infant", "title": "Infant (1)", "price": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "items": [{"id": "flight_infant-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 137534, "nanos": *********}, "items": []}, {"id": "flight_infant-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 9082, "nanos": *********}, "items": []}]}], "addedItems": []}}, {"token": "d6a1f_H4sIAAAAAAAA_y2QW3ObMBCFf038VAQCDFVmNB0P4BSbi7nFTl40IAQmpcZFyuDw66tCR9I539l92B1dhbjzZ1Vt-q69Cq50DLSDGNpSMECH32ozSqmG4Vd3a9WyG1X3lDkvR4gORaTCb5qqyEOf336w8t6BETTYu-ReGu0Ckqf-iZxS3_Gjlw17CIWPFG-6ioESKwihFTnFMPuPI7bANkzf0X5_WUp0ENgElhvEmWlnubUWR_zTPmfGYUk1Dl0-xRqf43kfy3eO53cv91AeubVT9Ikd5rVbfE2PSIvywPWMBIb8te-PkrVi_8bDPE1eP7gIs0mPs2kusmkKHO0RuHWyjGBU7giAaSO45KHk2FyJCgy1FWuBs8vB3rlrFNhCxtp6YGhYOtpw1jMquuF2ZF_YL2zDVuQ_ft70rXJ80p3y808lthKejJ28LdGkXhctSVFIq4iZSKOr1aTZNv-6jMDv1JTQEF1qRyDQAJT0QfyztJnY98kwq78XBLni7AEAAA..", "segments": [{"departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "departureTime": "2025-06-19T18:30:00", "arrivalTime": "2025-06-19T19:25:00", "legs": [{"departureTime": "2025-06-19T18:30:00", "arrivalTime": "2025-06-19T19:25:00", "departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "cabinClass": "ECONOMY", "flightInfo": {"facilities": [], "flightNumber": 737, "planeType": "", "carrierInfo": {"operatingCarrier": "IU", "marketingCarrier": "IU", "operatingCarrierDisclosureText": ""}}, "carriers": ["IU", "IU"], "carriersData": [{"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}, {"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}], "totalTime": 6900, "flightStops": [], "amenities": []}], "totalTime": 6900, "travellerCheckedLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}], "travellerCabinLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}}, "personalItem": true}], "isAtolProtected": false, "showWarningDestinationAirport": false, "showWarningOriginAirport": false}], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 999073, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 300463, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1297497}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1297497}, "carrierTaxBreakdown": [{"carrier": {"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}, "avgPerAdult": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "avgPerInfant": {"currencyCode": "IDR", "units": 9082, "nanos": *********}}], "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerPrices": [{"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 846524, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1149212}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1149212}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "1", "travellerType": "ADULT"}, {"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 137534, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 9082, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "2", "travellerType": "KID"}], "priceDisplayRequirements": [], "pointOfSale": "ie", "tripType": "ONEWAY", "posMismatch": {"detectedPointOfSale": "ie", "isPOSMismatch": false, "offerSalesCountry": "ie"}, "includedProductsBySegment": [[{"travellerReference": "1", "travellerProducts": [{"type": "checkedInBaggage", "product": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}, {"type": "cabinBaggage", "product": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}}}, {"type": "personalItem"}]}]], "includedProducts": {"areAllSegmentsIdentical": true, "segments": [[{"luggageType": "PERSONAL_ITEM", "maxPiece": 1, "piecePerPax": 1}, {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}, "piecePerPax": 1}, {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG", "piecePerPax": 1}]]}, "extraProducts": [{"type": "flexibleTicket", "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "carrierPriceBreakdown": []}}], "offerExtras": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "ancillaries": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "preSelected": false, "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "brandedFareInfo": {"fareName": "Economy", "cabinClass": "ECONOMY", "features": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}], "fareAttributes": [], "nonIncludedFeaturesRequired": false, "nonIncludedFeatures": [], "featuresList": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}]}, "appliedDiscounts": [], "offerKeyToHighlight": "1_IU737.DPS20250619", "extraProductDisplayRequirements": {}, "unifiedPriceBreakdown": {"price": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "items": [{"scope": "FLIGHT_ADULT", "id": "flight_adult", "title": "Adult (1)", "price": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "items": [{"id": "flight_adult-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 846524, "nanos": *********}, "items": []}, {"id": "flight_adult-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "items": []}]}, {"scope": "FLIGHT_INFANT", "id": "flight_infant", "title": "Infant (1)", "price": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "items": [{"id": "flight_infant-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 137534, "nanos": *********}, "items": []}, {"id": "flight_infant-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 9082, "nanos": *********}, "items": []}]}], "addedItems": []}}, {"token": "d6a1f_H4sIAAAAAAAA_y2QW2-jMBCFf03zVAzmEupKVpUCaUm4hFuT9sUixhC6bIiwK5L8-vVCZfuc78w8zMgnIS78WVXrrm1OgistA00v-qYUDND-r1oPUo59_6c9N2rZDqq7y5y3LUSbIlLho6Yq8tDnzxdWXlowgBp7h9xLo1VA8tTfkV3qO370tmBXofCB4kV7ZKDECkJoRk4xzH5xwEtghekXWq8PU4n2Aptg6QZxZtpZ_joXB_xu7zNjM6UKhy4f4_uXH9_XsXx7yV7uoTxyK6foEjvMK7e4jddIi_LA9YwEhvyj67aStWL9ycM8TT6-uQizUY-z8V5k4xg42jVwq2QawajcEQDTRnDKfcmxORMVGGozVgJnh429cuco8BIZc-uKobHU0YKzjlHR9uctu2G_sC1Lkf_4c9YtZfugO-WPMG9QwoOxkrchmtTTpCXZu9KOxEyk0dkqUlv1_y4j8ImaEmqiS20JBBqAkr6Jv5d2J_ZlNMzjP95S8uXsAQAA", "segments": [{"departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "departureTime": "2025-06-19T19:25:00", "arrivalTime": "2025-06-19T20:20:00", "legs": [{"departureTime": "2025-06-19T19:25:00", "arrivalTime": "2025-06-19T20:20:00", "departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "cabinClass": "ECONOMY", "flightInfo": {"facilities": [], "flightNumber": 755, "planeType": "", "carrierInfo": {"operatingCarrier": "IU", "marketingCarrier": "IU", "operatingCarrierDisclosureText": ""}}, "carriers": ["IU", "IU"], "carriersData": [{"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}, {"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}], "totalTime": 6900, "flightStops": [], "amenities": []}], "totalTime": 6900, "travellerCheckedLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}], "travellerCabinLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}}, "personalItem": true}], "isAtolProtected": false, "showWarningDestinationAirport": false, "showWarningOriginAirport": false}], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 999073, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 300463, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1297497}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1297497}, "carrierTaxBreakdown": [{"carrier": {"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}, "avgPerAdult": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "avgPerInfant": {"currencyCode": "IDR", "units": 9082, "nanos": *********}}], "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerPrices": [{"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 846524, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1149212}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1149212}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "1", "travellerType": "ADULT"}, {"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 137534, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 9082, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "2", "travellerType": "KID"}], "priceDisplayRequirements": [], "pointOfSale": "ie", "tripType": "ONEWAY", "posMismatch": {"detectedPointOfSale": "ie", "isPOSMismatch": false, "offerSalesCountry": "ie"}, "includedProductsBySegment": [[{"travellerReference": "1", "travellerProducts": [{"type": "checkedInBaggage", "product": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}, {"type": "cabinBaggage", "product": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}}}, {"type": "personalItem"}]}]], "includedProducts": {"areAllSegmentsIdentical": true, "segments": [[{"luggageType": "PERSONAL_ITEM", "maxPiece": 1, "piecePerPax": 1}, {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}, "piecePerPax": 1}, {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG", "piecePerPax": 1}]]}, "extraProducts": [{"type": "flexibleTicket", "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "carrierPriceBreakdown": []}}], "offerExtras": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "ancillaries": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "preSelected": false, "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "brandedFareInfo": {"fareName": "Economy", "cabinClass": "ECONOMY", "features": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}], "fareAttributes": [], "nonIncludedFeaturesRequired": false, "nonIncludedFeatures": [], "featuresList": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}]}, "appliedDiscounts": [], "offerKeyToHighlight": "1_IU755.DPS20250619", "extraProductDisplayRequirements": {}, "unifiedPriceBreakdown": {"price": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "items": [{"scope": "FLIGHT_ADULT", "id": "flight_adult", "title": "Adult (1)", "price": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "items": [{"id": "flight_adult-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 846524, "nanos": *********}, "items": []}, {"id": "flight_adult-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "items": []}]}, {"scope": "FLIGHT_INFANT", "id": "flight_infant", "title": "Infant (1)", "price": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "items": [{"id": "flight_infant-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 137534, "nanos": *********}, "items": []}, {"id": "flight_infant-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 9082, "nanos": *********}, "items": []}]}], "addedItems": []}}, {"token": "d6a1f_H4sIAAAAAAAA_y2QbY-iMBSFf834aSkUULaTNBsDOIvyIm-jzpcGS8G6rBjaCY6_fjuwaXvOc-69SZtepLyLV11vOt5epNA4A20v-7aSDND-r94MSs59_4ffWr3ig-7tc_dtB9G2jHX4w9A1tejr6Rer7hwMoMH-sfCzeB2SIgv2ZJ8FbhC_LdhDamKgeMHPDFRYQwjNKCiG-X8c8Aoso-wDbTbHqUR7iW2w8sIkt528OM3FAf92Drm1nVKNI0-MyfMjSJ6bRJ2DYr_wURF7tVt2qRMVtVd-jY_YiIvQ860URuK963aKjXJzElGRpe9XIaN8NJN8fJb5OIau8Qi9Op2uYFS9EQDbQXDKfSWwPROVGBoz1hLnx62z9uYo8QpZc-uBobUy0UKwjlHJ-9uOfeGgdGykqX_8vJlLbfdiutVY8auh4MVaq90SQ-ll0oqcoLIzsVNldLaaNMvmu8sI_EltBQ0xlXICgQG-568kOCh7Euc-Wvb5H_aXX-rsAQAA", "segments": [{"departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "departureTime": "2025-06-19T20:25:00", "arrivalTime": "2025-06-19T21:20:00", "legs": [{"departureTime": "2025-06-19T20:25:00", "arrivalTime": "2025-06-19T21:20:00", "departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "cabinClass": "ECONOMY", "flightInfo": {"facilities": [], "flightNumber": 749, "planeType": "", "carrierInfo": {"operatingCarrier": "IU", "marketingCarrier": "IU", "operatingCarrierDisclosureText": ""}}, "carriers": ["IU", "IU"], "carriersData": [{"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}, {"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}], "totalTime": 6900, "flightStops": [], "amenities": []}], "totalTime": 6900, "travellerCheckedLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}], "travellerCabinLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}}, "personalItem": true}], "isAtolProtected": false, "showWarningDestinationAirport": false, "showWarningOriginAirport": false}], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 999073, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 300463, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1297497}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1297497}, "carrierTaxBreakdown": [{"carrier": {"name": "Super Air Jet", "code": "IU", "logo": "https://r-xx.bstatic.com/data/airlines_logo/IU.png"}, "avgPerAdult": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "avgPerInfant": {"currencyCode": "IDR", "units": 9082, "nanos": *********}}], "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerPrices": [{"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 846524, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1149212}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1149212}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "1", "travellerType": "ADULT"}, {"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 137534, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 9082, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "2", "travellerType": "KID"}], "priceDisplayRequirements": [], "pointOfSale": "ie", "tripType": "ONEWAY", "posMismatch": {"detectedPointOfSale": "ie", "isPOSMismatch": false, "offerSalesCountry": "ie"}, "includedProductsBySegment": [[{"travellerReference": "1", "travellerProducts": [{"type": "checkedInBaggage", "product": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}, {"type": "cabinBaggage", "product": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}}}, {"type": "personalItem"}]}]], "includedProducts": {"areAllSegmentsIdentical": true, "segments": [[{"luggageType": "PERSONAL_ITEM", "maxPiece": 1, "piecePerPax": 1}, {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 40, "maxWidth": 30, "maxHeight": 20, "sizeUnit": "CM"}, "piecePerPax": 1}, {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG", "piecePerPax": 1}]]}, "extraProducts": [{"type": "flexibleTicket", "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "carrierPriceBreakdown": []}}], "offerExtras": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "ancillaries": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "preSelected": false, "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "brandedFareInfo": {"fareName": "Economy", "cabinClass": "ECONOMY", "features": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}], "fareAttributes": [], "nonIncludedFeaturesRequired": false, "nonIncludedFeatures": [], "featuresList": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}]}, "appliedDiscounts": [], "offerKeyToHighlight": "1_IU749.DPS20250619", "extraProductDisplayRequirements": {}, "unifiedPriceBreakdown": {"price": {"currencyCode": "IDR", "units": 1284522, "nanos": *********}, "items": [{"scope": "FLIGHT_ADULT", "id": "flight_adult", "title": "Adult (1)", "price": {"currencyCode": "IDR", "units": 1137905, "nanos": *********}, "items": [{"id": "flight_adult-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 846524, "nanos": *********}, "items": []}, {"id": "flight_adult-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 291380, "nanos": *********}, "items": []}]}, {"scope": "FLIGHT_INFANT", "id": "flight_infant", "title": "Infant (1)", "price": {"currencyCode": "IDR", "units": 146617, "nanos": *********}, "items": [{"id": "flight_infant-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 137534, "nanos": *********}, "items": []}, {"id": "flight_infant-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 9082, "nanos": *********}, "items": []}]}], "addedItems": []}}, {"token": "d6a1f_H4sIAAAAAAAA_y2QW2-jMBCFf03ztBibUAiVrCoCktJwCbckzYsFxiF02ZDFrkj59evCyjPnfGdkySNfhbjzF1W9tE19FVxpGKg70dWFYIB2f9RLL6Xsut_NrVaLpledfWpvd8h6z0MV_YKqIg99-Xhlxb0BPbhg95S5Sbj2SZZ4e7JPPNsLtwv2EArvKV40JQMFVizLmpFTjNL_2GMDPAfJ2dpsTtOIdgLrwHD8KNXNNDvMwx6_rTx_dZxShQOHD9G4yWVHso_ReHYz18pCp7LzNjaDrHLy7-ERwjDzHXcZo4Af2nYnGeabDx5kSXz45CJIBy1KhzFPh8G34cN3qnh6glG5IwC6aaEpdwXH-kxUYARnrAROT-_m2pmjwKYOzYkfGC0NzVpw1jIqmu62Y984Pq_gUpH_-HXTnhX_SbNLaIxlK-FpuZZVEyj1OmlBtrq0kuixNDpbRRBcWZokRtCK_ly4kJ_YEAQgQJI-iXeUNhKTGd397z8KEKPW7QEAAA..", "segments": [{"departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "departureTime": "2025-06-19T09:40:00", "arrivalTime": "2025-06-19T10:30:00", "legs": [{"departureTime": "2025-06-19T09:40:00", "arrivalTime": "2025-06-19T10:30:00", "departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "cabinClass": "ECONOMY", "flightInfo": {"facilities": [], "flightNumber": 803, "planeType": "", "carrierInfo": {"operatingCarrier": "QZ", "marketingCarrier": "QZ", "operatingCarrierDisclosureText": ""}}, "carriers": ["QZ", "QZ"], "carriersData": [{"name": "PT Indonesia Airasia", "code": "QZ", "logo": "https://r-xx.bstatic.com/data/airlines_logo/QZ.png"}, {"name": "PT Indonesia Airasia", "code": "QZ", "logo": "https://r-xx.bstatic.com/data/airlines_logo/QZ.png"}], "totalTime": 6600, "flightStops": [], "amenities": []}], "totalTime": 6600, "travellerCheckedLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}], "travellerCabinLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 56, "maxWidth": 36, "maxHeight": 23, "sizeUnit": "CM"}}, "personalItem": true}], "isAtolProtected": false, "showWarningDestinationAirport": false, "showWarningOriginAirport": false}], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 1384615, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 1078220, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 294717, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": 90000000, "units": 1390176}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1384615, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": 90000000, "units": 1390176}, "carrierTaxBreakdown": [{"carrier": {"name": "PT Indonesia Airasia", "code": "QZ", "logo": "https://r-xx.bstatic.com/data/airlines_logo/QZ.png"}, "avgPerAdult": {"currencyCode": "IDR", "units": 294717, "nanos": *********}}], "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerPrices": [{"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 1218350, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 923632, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 294717, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1223354}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1218350, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1223354}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "1", "travellerType": "ADULT"}, {"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 154587, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 154587, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 166821}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 154587, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 166821}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "2", "travellerType": "KID"}], "priceDisplayRequirements": [], "pointOfSale": "ie", "tripType": "ONEWAY", "posMismatch": {"detectedPointOfSale": "ie", "isPOSMismatch": false, "offerSalesCountry": "ie"}, "includedProductsBySegment": [[{"travellerReference": "1", "travellerProducts": [{"type": "checkedInBaggage", "product": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}, {"type": "cabinBaggage", "product": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 56, "maxWidth": 36, "maxHeight": 23, "sizeUnit": "CM"}}}, {"type": "personalItem"}]}]], "includedProducts": {"areAllSegmentsIdentical": true, "segments": [[{"luggageType": "PERSONAL_ITEM", "maxPiece": 1, "piecePerPax": 1}, {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 56, "maxWidth": 36, "maxHeight": 23, "sizeUnit": "CM"}, "piecePerPax": 1}, {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG", "piecePerPax": 1}]]}, "extraProducts": [{"type": "flexibleTicket", "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "carrierPriceBreakdown": []}}], "offerExtras": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "ancillaries": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "preSelected": false, "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "brandedFareInfo": {"fareName": "Low fare", "cabinClass": "ECONOMY", "features": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}], "fareAttributes": [], "nonIncludedFeaturesRequired": false, "nonIncludedFeatures": [], "featuresList": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}]}, "fareRules": [{"segmentIdentifiers": [{"segmentIndex": 0, "legIndex": 0}], "availablePolicies": [], "unavailablePolicies": [{"type": "CANCEL_BEFORE"}]}], "appliedDiscounts": [], "offerKeyToHighlight": "1_QZ803.DPS20250619", "extraProductDisplayRequirements": {}, "unifiedPriceBreakdown": {"price": {"currencyCode": "IDR", "units": 1384615, "nanos": *********}, "items": [{"scope": "FLIGHT_ADULT", "id": "flight_adult", "title": "Adult (1)", "price": {"currencyCode": "IDR", "units": 1218350, "nanos": *********}, "items": [{"id": "flight_adult-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 923632, "nanos": *********}, "items": []}, {"id": "flight_adult-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 294717, "nanos": *********}, "items": []}]}, {"scope": "FLIGHT_INFANT", "id": "flight_infant", "title": "Infant (1)", "price": {"currencyCode": "IDR", "units": 154587, "nanos": *********}, "items": [{"id": "flight_infant-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 154587, "nanos": *********}, "items": []}]}], "addedItems": []}}, {"token": "d6a1f_H4sIAAAAAAAA_y2QbU-zMBiFf437JIUyHNSkMQswxfEy3tzcl4aVjqG4TtonzP36p4Jp73Ouc9KkTU9SXsSjrh-7tjlJobUMNFzyppIMUP6lH3slB84_23OjV22ve5vcfV5D9FrGOrw3dE0t-vj-xKpLC3pwxP6u8LN4GZIiCzZkkwVuED_P2FVqoqd41h4YqLCGEJpQUAzzP-zxAjxE2R6tVruxolxiCyy8MMktOy_cqezxixOEznZMNY48MSS3VakmUbNNbnu_8FERe7VbdqkdFbVX_gzX2IiL0PPnKYzEW9etFRvl6l1ERZa-fQgZ5YOZ5MOtzIchdI1r6NXpeAWj6o0AWDaCY-aVwNZEVGJoTFhLnO9e7aU3RYlty7BHvmI4X5hoJljHqGz5ec1-cLp3INLUP_47mw9aeGe6h7mwP7mCu_lS7YYYSk-jVgQalvIDsVJldLJatQ4yFTECHfp74Eh-Y0sgMABU9EGCrbIbsdmCX77_A9Ju3YDuAQAA", "segments": [{"departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "departureTime": "2025-06-19T21:40:00", "arrivalTime": "2025-06-19T22:30:00", "legs": [{"departureTime": "2025-06-19T21:40:00", "arrivalTime": "2025-06-19T22:30:00", "departureAirport": {"type": "AIRPORT", "code": "DPS", "name": "Ngurah Rai International Airport", "city": "DPS", "cityName": "<PERSON><PERSON>", "country": "ID", "countryName": "Indonesia", "province": "Bali"}, "arrivalAirport": {"type": "AIRPORT", "code": "CGK", "name": "Soekarno-Hatta International Airport", "city": "JKT", "cityName": "Jakarta", "country": "ID", "countryName": "Indonesia", "province": "Jakarta Province"}, "cabinClass": "ECONOMY", "flightInfo": {"facilities": [], "flightNumber": 819, "planeType": "", "carrierInfo": {"operatingCarrier": "QZ", "marketingCarrier": "QZ", "operatingCarrierDisclosureText": ""}}, "carriers": ["QZ", "QZ"], "carriersData": [{"name": "PT Indonesia Airasia", "code": "QZ", "logo": "https://r-xx.bstatic.com/data/airlines_logo/QZ.png"}, {"name": "PT Indonesia Airasia", "code": "QZ", "logo": "https://r-xx.bstatic.com/data/airlines_logo/QZ.png"}], "totalTime": 6600, "flightStops": [], "amenities": []}], "totalTime": 6600, "travellerCheckedLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}], "travellerCabinLuggage": [{"travellerReference": "1", "luggageAllowance": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 56, "maxWidth": 36, "maxHeight": 23, "sizeUnit": "CM"}}, "personalItem": true}], "isAtolProtected": false, "showWarningDestinationAirport": false, "showWarningOriginAirport": false}], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 1384615, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 1078220, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 294717, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": 90000000, "units": 1390176}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1384615, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": 90000000, "units": 1390176}, "carrierTaxBreakdown": [{"carrier": {"name": "PT Indonesia Airasia", "code": "QZ", "logo": "https://r-xx.bstatic.com/data/airlines_logo/QZ.png"}, "avgPerAdult": {"currencyCode": "IDR", "units": 294717, "nanos": *********}}], "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerPrices": [{"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 1218350, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 923632, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 294717, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1223354}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1218350, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1223354}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "1", "travellerType": "ADULT"}, {"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 154587, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 154587, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 166821}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 154587, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 166821}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "travellerReference": "2", "travellerType": "KID"}], "priceDisplayRequirements": [], "pointOfSale": "ie", "tripType": "ONEWAY", "posMismatch": {"detectedPointOfSale": "ie", "isPOSMismatch": false, "offerSalesCountry": "ie"}, "includedProductsBySegment": [[{"travellerReference": "1", "travellerProducts": [{"type": "checkedInBaggage", "product": {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG"}}, {"type": "cabinBaggage", "product": {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 56, "maxWidth": 36, "maxHeight": 23, "sizeUnit": "CM"}}}, {"type": "personalItem"}]}]], "includedProducts": {"areAllSegmentsIdentical": true, "segments": [[{"luggageType": "PERSONAL_ITEM", "maxPiece": 1, "piecePerPax": 1}, {"luggageType": "HAND", "maxPiece": 1, "maxWeightPerPiece": 7, "massUnit": "KG", "sizeRestrictions": {"maxLength": 56, "maxWidth": 36, "maxHeight": 23, "sizeUnit": "CM"}, "piecePerPax": 1}, {"luggageType": "CHECKED_IN", "ruleType": "PIECE_BASED", "maxPiece": 1, "maxWeightPerPiece": 20, "massUnit": "KG", "piecePerPax": 1}]]}, "extraProducts": [{"type": "flexibleTicket", "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "carrierPriceBreakdown": []}}], "offerExtras": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 185356}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "ancillaries": {"flexibleTicket": {"airProductReference": "n/a", "travellers": ["1"], "priceBreakdown": {"total": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 183503, "nanos": *********}, "showPriceStrikethrough": false, "carrierPriceBreakdown": []}, "preSelected": false, "recommendation": {"recommended": false, "confidence": "UNKNOWN_LEVEL"}, "supplierInfo": {"name": "OY SRG Finland Ab", "termsUrl": "https://flights-ie.gotogate.com/rf/travel-conditions", "privacyPolicyUrl": "https://flights-ie.gotogate.com/rf/privacy-policy"}}}, "brandedFareInfo": {"fareName": "Low fare", "cabinClass": "ECONOMY", "features": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}], "fareAttributes": [], "nonIncludedFeaturesRequired": false, "nonIncludedFeatures": [], "featuresList": [{"featureName": "PERSONAL_BAGGAGE", "category": "BAGGAGE", "code": "BK03", "label": "1 personal items", "availability": "INCLUDED", "icon": "PERSONAL_LUGGAGE_ICON"}, {"featureName": "CABIN_BAGGAGE", "category": "BAGGAGE", "code": "BK02", "label": "1 cabin bag (1 kg)\t", "availability": "INCLUDED", "icon": "CABIN_LUGGAGE_ICON"}, {"featureName": "CHECK_BAGGAGE", "category": "BAGGAGE", "code": "BK01", "label": "1 checked bag (1 kg in total)", "availability": "INCLUDED", "icon": "CHECKED_IN_LUGGAGE_ICON"}]}, "fareRules": [{"segmentIdentifiers": [{"segmentIndex": 0, "legIndex": 0}], "availablePolicies": [], "unavailablePolicies": [{"type": "CANCEL_BEFORE"}]}], "appliedDiscounts": [], "offerKeyToHighlight": "1_QZ819.DPS20250619", "extraProductDisplayRequirements": {}, "unifiedPriceBreakdown": {"price": {"currencyCode": "IDR", "units": 1384615, "nanos": *********}, "items": [{"scope": "FLIGHT_ADULT", "id": "flight_adult", "title": "Adult (1)", "price": {"currencyCode": "IDR", "units": 1218350, "nanos": *********}, "items": [{"id": "flight_adult-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 923632, "nanos": *********}, "items": []}, {"id": "flight_adult-tax", "title": "Airline taxes and charges", "price": {"currencyCode": "IDR", "units": 294717, "nanos": *********}, "items": []}]}, {"scope": "FLIGHT_INFANT", "id": "flight_infant", "title": "Infant (1)", "price": {"currencyCode": "IDR", "units": 154587, "nanos": *********}, "items": [{"id": "flight_infant-basefare", "title": "Flight fare", "price": {"currencyCode": "IDR", "units": 154587, "nanos": *********}, "items": []}]}], "addedItems": []}}], "flightDeals": [{"key": "CHEAPEST", "offerToken": "d6a1f_H4sIAAAAAAAA_y2QW2-jMBCFf03zVAzmWipZq4hLl4ZLuLVpXixiDKFLQ4RdkfDr68LK9jnfGY00I585v7JnWW76rj1zJnUUtAMf2opTQIYvuRmFnIbhX3dp5aobZXefOy87aL-WsQwfFVkShzx__KHVtQMjaJB3KLws3oa4yII93meBE8QvG3rjEhsJ2nQnCiok2ba9IiMI5v9xRCYwouxo-_5hKZGBIx2YbpjkupUX4Voc0d8nzfPLJdUoctmUKGxOZj8R7z2Zj17h2UXs1k7Zp1ZU1G55n26xEheh62kpjNhb3-8EK6X_waIiS98-GY_ySU3yaS7u0xQ6yi1063QZQYnYEQDdsuGSh4ohfSXCEVRWrDnKD6_W1l0jR6ZhGAvfENRM1d4w2lPCu-Gyo3cUlJZuSOIfvy-qIYUPqlN918PVEPCgbcVtsSL0vGiFj6WwE9ZTYWS1GlPLoAIohk-n37YGq0I7DIECoKBPHLwLm7F1JY2p_QAo_32B7AEAAA..", "price": {"currencyCode": "IDR", "units": 1215013, "nanos": *********}, "priceRounded": {"currencyCode": "IDR", "units": 1223354, "nanos": *********}, "travellerPrices": [{"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 1074884, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 786468, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 288415, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1075069}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1074884, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1075069}, "carrierPriceBreakdown": []}, "travellerReference": "1", "travellerType": "ADULT"}, {"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 140129, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 146431, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 8711, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 140129, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "carrierPriceBreakdown": []}, "travellerReference": "2", "travellerType": "KID"}]}, {"key": "FASTEST", "offerToken": "d6a1f_H4sIAAAAAAAA_y2QXXOiMBiFf029WkKCiKYzmR0XsEvlQz601psMQkC6rOmQrCi_flPoJDnnOefmfScXKT_Fs65XbVNfpNAaBmoueZ1LBgr-V686JWfO_zTXWs-bTnd2qf2yRfh1H-roB9Q1dYrn958s_2xAByriHjM3Cdc-zRJvR3eJZ3vhy4zdpSa6gsyaMwM50TDGE4qCoPQbO2KBRZCc8GZzHKuCS2ICy_Gj1FymWTSVHfmNktBbj6kkgSP6CJ7caNhE6r1Fw8nNXJyFTmnv23gZZKWzf_T3EIaZ77jzGAXi0LZbxXC_eRdBlsSHDyGDtDeitB8Su-99G959p4zHEaxQOwJgLjEaM88FMScqJEFwwlKS9Pi6XDtTlMTCeD7ynaC5ZeCZYC0rZMOvW_Ygq18LBLGmPvLf1VhouyfDPi8et7JV8DRfq1tTqPQyak4PWNmZmrGyYrKSViu2UsAoWpmGgop-aUMRgAAp-qDem7KBWjc43Ph_0mi6F-0BAAA.", "price": {"currencyCode": "IDR", "units": 1296200, "nanos": *********}, "priceRounded": {"currencyCode": "IDR", "units": 1297497, "nanos": *********}, "travellerPrices": [{"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 1165894, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 845227, "nanos": 60000000}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 305653, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1167747}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1165894, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1167747}, "carrierPriceBreakdown": []}, "travellerReference": "1", "travellerType": "ADULT"}, {"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 145319, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 136422, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 8897, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 145319, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "carrierPriceBreakdown": []}, "travellerReference": "2", "travellerType": "KID"}]}, {"key": "BEST", "offerToken": "d6a1f_H4sIAAAAAAAA_y2QXXOiMBiFf029WkKCiKYzmR0XsEvlQz601psMQkC6rOmQrCi_flPoJDnnOefmfScXKT_Fs65XbVNfpNAaBmoueZ1LBgr-V686JWfO_zTXWs-bTnd2qf2yRfh1H-roB9Q1dYrn958s_2xAByriHjM3Cdc-zRJvR3eJZ3vhy4zdpSa6gsyaMwM50TDGE4qCoPQbO2KBRZCc8GZzHKuCS2ICy_Gj1FymWTSVHfmNktBbj6kkgSP6CJ7caNhE6r1Fw8nNXJyFTmnv23gZZKWzf_T3EIaZ77jzGAXi0LZbxXC_eRdBlsSHDyGDtDeitB8Su-99G959p4zHEaxQOwJgLjEaM88FMScqJEFwwlKS9Pi6XDtTlMTCeD7ynaC5ZeCZYC0rZMOvW_Ygq18LBLGmPvLf1VhouyfDPi8et7JV8DRfq1tTqPQyak4PWNmZmrGyYrKSViu2UsAoWpmGgop-aUMRgAAp-qDem7KBWjc43Ph_0mi6F-0BAAA.", "price": {"currencyCode": "IDR", "units": 1296200, "nanos": *********}, "priceRounded": {"currencyCode": "IDR", "units": 1297497, "nanos": *********}, "travellerPrices": [{"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 1165894, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 845227, "nanos": 60000000}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 305653, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1167747}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 1165894, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 1167747}, "carrierPriceBreakdown": []}, "travellerReference": "1", "travellerType": "ADULT"}, {"travellerPriceBreakdown": {"total": {"currencyCode": "IDR", "units": 145319, "nanos": *********}, "baseFare": {"currencyCode": "IDR", "units": 136422, "nanos": *********}, "fee": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "tax": {"currencyCode": "IDR", "units": 8897, "nanos": *********}, "totalRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "discount": {"currencyCode": "IDR", "units": 0, "nanos": 0}, "totalWithoutDiscount": {"currencyCode": "IDR", "units": 145319, "nanos": *********}, "totalWithoutDiscountRounded": {"currencyCode": "IDR", "nanos": *********, "units": 148285}, "carrierPriceBreakdown": []}, "travellerReference": "2", "travellerType": "KID"}]}], "atolProtectedStatus": "NONE", "searchId": "B11A4072A30AA3853D4545868A11D42A", "banners": [], "displayOptions": {"brandedFaresShownByDefault": false, "directFlightsOnlyFilterIgnored": false, "hideFlexiblePricesBanner": false}, "isOffersCabinClassExtended": false, "cabinClassExtension": {}, "searchCriteria": {"cabinClass": "ECONOMY"}, "personalisationSegments": ["FRUGAL_COMPANIONS", "SHORT_HAUL_TRIP", "FAMILY_TRAVELLERS"], "priceAlertStatus": {"isEligible": false, "isSearchEligible": true, "isBlockoutEligible": true}}}