[project]
name = "tripadvisor_mcp"
version = "0.1.0"
description = "MCP server for Tripadvisor Content API integration"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "mcp[cli]",
    "httpx",
    "python-dotenv",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.10.0",
]

[project.scripts]
tripadvisor-mcp = "tripadvisor_mcp.main:run_server"

[tool.setuptools]
packages = ["tripadvisor_mcp"]
package-dir = {"" = "src"}

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
python_classes = "Test*"
addopts = "--cov=src --cov-report=term-missing"

[tool.coverage.run]
source = ["src/tripadvisor_mcp"]
omit = ["*/__pycache__/*", "*/tests/*", "*/.venv/*", "*/venv/*"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "pass",
    "raise ImportError"
]
