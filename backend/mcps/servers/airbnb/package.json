{"name": "@openbnb/mcp-server-airbnb", "version": "0.1.1", "description": "MCP server for Airbnb search and listing details", "license": "MIT", "type": "module", "author": "OpenBnB (https://openbnb.org)", "keywords": ["mcp", "airbnb", "vacation rental"], "publishConfig": {"access": "public"}, "bin": {"mcp-server-airbnb": "dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && shx chmod +x dist/*.js", "prepare": "npm run build", "watch": "tsc --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.0.1", "cheerio": "^1.0.0", "node-fetch": "^3.3.2", "robots-parser": "^3.0.1"}, "devDependencies": {"@types/node": "^22.13.9", "@types/node-fetch": "^2.6.12", "shx": "^0.3.4", "typescript": "^5.8.2"}}