{"name": "@modelcontextprotocol/inspector", "version": "0.14.0", "description": "Model Context Protocol inspector", "license": "MIT", "author": "An<PERSON><PERSON>, PBC (https://anthropic.com)", "homepage": "https://modelcontextprotocol.io", "bugs": "https://github.com/modelcontextprotocol/inspector/issues", "type": "module", "bin": {"mcp-inspector": "cli/build/cli.js"}, "files": ["client/bin", "client/dist", "server/build", "cli/build"], "workspaces": ["client", "server", "cli"], "scripts": {"build": "npm run build-server && npm run build-client && npm run build-cli", "build-server": "cd server && npm run build", "build-client": "cd client && npm run build", "build-cli": "cd cli && npm run build", "clean": "rimraf ./node_modules ./client/node_modules ./cli/node_modules ./build ./client/dist ./server/build ./cli/build ./package-lock.json && npm install", "dev": "concurrently \"cd client && npm run dev\" \"cd server && npm run dev\"", "dev:windows": "concurrently \"cd client && npm run dev\" \"cd server && npm run dev:windows\"", "start": "node client/bin/start.js", "start-server": "cd server && npm run start", "start-client": "cd client && npm run preview", "test": "npm run prettier-check && cd client && npm test", "test-cli": "cd cli && npm run test", "prettier-fix": "prettier --write .", "prettier-check": "prettier --check .", "lint": "prettier --check . && cd client && npm run lint", "prepare": "npm run build", "publish-all": "npm publish --workspaces --access public && npm publish --access public"}, "dependencies": {"@modelcontextprotocol/inspector-cli": "^0.14.0", "@modelcontextprotocol/inspector-client": "^0.14.0", "@modelcontextprotocol/inspector-server": "^0.14.0", "@modelcontextprotocol/sdk": "^1.12.1", "concurrently": "^9.0.1", "open": "^10.1.0", "shell-quote": "^1.8.2", "spawn-rx": "^5.1.2", "ts-node": "^10.9.2", "zod": "^3.23.8"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^22.7.5", "@types/shell-quote": "^1.7.5", "jest-fixed-jsdom": "^0.0.9", "prettier": "3.3.3", "rimraf": "^6.0.1", "typescript": "^5.4.2"}}