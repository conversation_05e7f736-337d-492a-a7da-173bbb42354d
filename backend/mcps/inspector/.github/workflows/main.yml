on:
  push:
    branches:
      - main

  pull_request:
  release:
    types: [published]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Check formatting
        run: npx prettier --check .

      - uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: npm

      # Working around https://github.com/npm/cli/issues/4828
      # - run: npm ci
      - run: npm install --no-package-lock

      - name: Check linting
        working-directory: ./client
        run: npm run lint

      - name: Run client tests
        working-directory: ./client
        run: npm test

      - run: npm run build

  publish:
    runs-on: ubuntu-latest
    if: github.event_name == 'release'
    environment: release
    needs: build

    permissions:
      contents: read
      id-token: write

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: npm
          registry-url: "https://registry.npmjs.org"

      # Working around https://github.com/npm/cli/issues/4828
      # - run: npm ci
      - run: npm install --no-package-lock

      # TODO: Add --provenance once the repo is public
      - run: npm run publish-all
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
